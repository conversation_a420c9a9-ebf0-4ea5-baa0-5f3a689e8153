#!/usr/bin/env python3
"""
Simple test script for Custom Commands functionality.
This script tests the argument substitution logic without requiring a full bot setup.
"""

import sys
import os

# Add the project root to the path so we can import our modules
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_argument_substitution():
    """Test the argument substitution functionality"""
    
    def substitute_arguments(command, args):
        """Substitute {0}, {1}, etc. with user arguments"""
        if not args:
            return command
        
        # Replace {0}, {1}, {2}, etc. with actual arguments
        for i, arg in enumerate(args):
            command = command.replace(f"{{{i}}}", arg)
        
        return command
    
    # Test cases
    test_cases = [
        {
            "command": "timeout {0} 10m",
            "args": ["@jonathan"],
            "expected": "timeout @jonathan 10m"
        },
        {
            "command": "role {0} @image-role",
            "args": ["@jonathan"],
            "expected": "role @jonathan @image-role"
        },
        {
            "command": "ban {0} {1}",
            "args": ["@jonathan", "Harassing Members"],
            "expected": "ban @jonathan Harassing Members"
        },
        {
            "command": "kick {0}",
            "args": [],
            "expected": "kick {0}"  # No substitution if no args
        },
        {
            "command": "mute {0} {1} {2}",
            "args": ["@user", "1h", "spam"],
            "expected": "mute @user 1h spam"
        }
    ]
    
    print("Testing argument substitution...")
    all_passed = True
    
    for i, test in enumerate(test_cases):
        result = substitute_arguments(test["command"], test["args"])
        if result == test["expected"]:
            print(f"[PASS] Test {i+1}: PASSED")
            print(f"   Input: '{test['command']}' with args {test['args']}")
            print(f"   Output: '{result}'")
        else:
            print(f"[FAIL] Test {i+1}: FAILED")
            print(f"   Input: '{test['command']}' with args {test['args']}")
            print(f"   Expected: '{test['expected']}'")
            print(f"   Got: '{result}'")
            all_passed = False
        print()
    
    return all_passed

def test_alias_validation():
    """Test alias name validation logic"""
    
    def is_valid_alias_name(name):
        """Check if alias name is valid"""
        if not name:
            return False, "Alias name cannot be empty"
        
        if len(name) > 32:
            return False, "Alias name too long (max 32 characters)"
        
        if ' ' in name:
            return False, "Alias name cannot contain spaces"
        
        # Check for invalid characters
        invalid_chars = ['@', '#', '!', '?', '&', '%', '*', '+', '=', '|', '\\', '/', '<', '>', '"', "'"]
        for char in invalid_chars:
            if char in name:
                return False, f"Alias name cannot contain '{char}'"
        
        return True, "Valid"
    
    test_cases = [
        ("ban", True),
        ("pic", True),
        ("shh", True),
        ("", False),
        ("this_is_a_very_long_alias_name_that_exceeds_limit", False),
        ("alias with spaces", False),
        ("alias@invalid", False),
        ("alias#invalid", False),
        ("valid_alias_123", True),
        ("ValidAlias", True)
    ]
    
    print("Testing alias name validation...")
    all_passed = True
    
    for alias_name, should_be_valid in test_cases:
        is_valid, message = is_valid_alias_name(alias_name)
        
        if is_valid == should_be_valid:
            status = "[PASS] PASSED"
        else:
            status = "[FAIL] FAILED"
            all_passed = False
        
        print(f"{status}: '{alias_name}' -> {message}")
    
    print()
    return all_passed

def main():
    """Run all tests"""
    print("=" * 50)
    print("Custom Commands Test Suite")
    print("=" * 50)
    print()
    
    test1_passed = test_argument_substitution()
    test2_passed = test_alias_validation()
    
    print("=" * 50)
    if test1_passed and test2_passed:
        print("All tests PASSED!")
        return 0
    else:
        print("Some tests FAILED!")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)

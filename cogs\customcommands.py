import discord
from discord.ext import commands
from utilities import decorators, checks, converters, pagination
import re


async def setup(bot):
    await bot.add_cog(CustomCommands(bot))


class CustomCommands(commands.Cog):
    """
    Create custom command aliases that run other commands.
    """

    def __init__(self, bot):
        self.bot = bot
        self.aliases_cache = {}  # Cache for server aliases
        bot.loop.create_task(self.load_aliases())

    async def load_aliases(self):
        """Load all aliases from database into cache"""
        if not self.bot.cxn:
            return
        try:
            query = """
                    SELECT server_id, alias_name, command
                    FROM aliases;
                    """
            records = await self.bot.cxn.fetch(query)
            for record in records:
                server_id = record['server_id']
                if server_id not in self.aliases_cache:
                    self.aliases_cache[server_id] = {}
                self.aliases_cache[server_id][record['alias_name']] = record['command']
        except Exception as e:
            print(f"Failed to load aliases: {e}")

    async def get_alias(self, server_id, alias_name):
        """Get an alias command for a server"""
        if server_id in self.aliases_cache:
            return self.aliases_cache[server_id].get(alias_name)
        return None

    async def add_alias_to_cache(self, server_id, alias_name, command):
        """Add alias to cache"""
        if server_id not in self.aliases_cache:
            self.aliases_cache[server_id] = {}
        self.aliases_cache[server_id][alias_name] = command

    async def remove_alias_from_cache(self, server_id, alias_name):
        """Remove alias from cache"""
        if server_id in self.aliases_cache and alias_name in self.aliases_cache[server_id]:
            del self.aliases_cache[server_id][alias_name]

    def substitute_arguments(self, command, args):
        """Substitute {0}, {1}, etc. with user arguments"""
        if not args:
            return command

        # Replace {0}, {1}, {2}, etc. with actual arguments
        for i, arg in enumerate(args):
            command = command.replace(f"{{{i}}}", arg)

        return command

    def validate_alias_name(self, name):
        """Validate alias name"""
        if not name:
            return False, "Alias name cannot be empty"

        if len(name) > 32:
            return False, "Alias name too long (max 32 characters)"

        if ' ' in name:
            return False, "Alias name cannot contain spaces"

        # Check for invalid characters
        invalid_chars = ['@', '#', '!', '?', '&', '%', '*', '+', '=', '|', '\\', '/', '<', '>', '"', "'"]
        for char in invalid_chars:
            if char in name:
                return False, f"Alias name cannot contain '{char}'"

        return True, "Valid"

    @decorators.group(
        invoke_without_command=True,
        brief="Manage command aliases.",
        case_insensitive=True
    )
    @checks.guild_only()
    async def alias(self, ctx):
        """
        Usage: {0}alias <subcommand>
        Output: Manage command aliases for your server
        Subcommands:
            {0}alias add <shortcut> <command> - Create a new alias
            {0}alias remove <shortcut> - Remove an alias
            {0}alias view <shortcut> - View what an alias does
            {0}alias list - List all server aliases
        """
        if ctx.invoked_subcommand is None:
            return await ctx.usage()

    @alias.command(name="add", brief="Create a new command alias")
    @checks.guild_only()
    @checks.has_perms(manage_guild=True)
    @checks.cooldown()
    async def alias_add(self, ctx, shortcut: str, *, command: str):
        """
        Usage: {0}alias add <shortcut> <command>
        Permission: Manage Guild
        Output: Creates a new command alias
        Notes:
            Use {0}, {1}, {2}, etc. for user arguments
            Example: {0}alias add ban timeout {0} 10m
        """
        if not shortcut or not command:
            return await ctx.usage()

        # Validate alias name
        is_valid, error_msg = self.validate_alias_name(shortcut)
        if not is_valid:
            return await ctx.fail(error_msg)

        # Check if shortcut already exists as a real command
        if self.bot.get_command(shortcut):
            return await ctx.fail(f"Cannot create alias `{shortcut}` - a command with that name already exists.")

        # Check if alias already exists
        existing = await self.get_alias(ctx.guild.id, shortcut)
        if existing:
            return await ctx.fail(f"Alias `{shortcut}` already exists. Use `{ctx.prefix}alias remove {shortcut}` first.")

        # Validate that the command exists (extract base command)
        base_command = command.split()[0]
        if not self.bot.get_command(base_command):
            return await ctx.fail(f"Command `{base_command}` does not exist.")

        try:
            # Add to database
            query = """
                    INSERT INTO aliases (server_id, alias_name, command, created_by)
                    VALUES ($1, $2, $3, $4);
                    """
            await self.bot.cxn.execute(query, ctx.guild.id, shortcut, command, ctx.author.id)
            
            # Add to cache
            await self.add_alias_to_cache(ctx.guild.id, shortcut, command)
            
            await ctx.success(f"Created alias `{shortcut}` → `{command}`")
        except Exception as e:
            await ctx.fail(f"Failed to create alias: {e}")

    @alias.command(name="remove", aliases=["delete", "rm"], brief="Remove a command alias")
    @checks.guild_only()
    @checks.has_perms(manage_guild=True)
    @checks.cooldown()
    async def alias_remove(self, ctx, shortcut: str):
        """
        Usage: {0}alias remove <shortcut>
        Aliases: {0}alias delete, {0}alias rm
        Permission: Manage Guild
        Output: Removes a command alias
        """
        if not shortcut:
            return await ctx.usage()

        # Check if alias exists
        existing = await self.get_alias(ctx.guild.id, shortcut)
        if not existing:
            return await ctx.fail(f"Alias `{shortcut}` does not exist.")

        try:
            # Remove from database
            query = """
                    DELETE FROM aliases
                    WHERE server_id = $1 AND alias_name = $2;
                    """
            await self.bot.cxn.execute(query, ctx.guild.id, shortcut)
            
            # Remove from cache
            await self.remove_alias_from_cache(ctx.guild.id, shortcut)
            
            await ctx.success(f"Removed alias `{shortcut}`")
        except Exception as e:
            await ctx.fail(f"Failed to remove alias: {e}")

    @alias.command(name="view", brief="View what an alias does")
    @checks.guild_only()
    @checks.cooldown()
    async def alias_view(self, ctx, shortcut: str):
        """
        Usage: {0}alias view <shortcut>
        Output: Shows what command an alias executes
        """
        if not shortcut:
            return await ctx.usage()

        command = await self.get_alias(ctx.guild.id, shortcut)
        if not command:
            return await ctx.fail(f"Alias `{shortcut}` does not exist.")

        embed = discord.Embed(
            title=f"Alias: {shortcut}",
            description=f"**Command:** `{command}`",
            color=0x323339
        )
        await ctx.send_or_reply(embed=embed)

    @alias.command(name="list", brief="List all server aliases")
    @checks.guild_only()
    @checks.cooldown()
    async def alias_list(self, ctx):
        """
        Usage: {0}alias list
        Output: Shows all aliases for this server
        """
        if ctx.guild.id not in self.aliases_cache or not self.aliases_cache[ctx.guild.id]:
            return await ctx.fail("This server has no aliases.")

        aliases = self.aliases_cache[ctx.guild.id]
        
        embed = discord.Embed(
            title=f"Aliases for {ctx.guild.name}",
            color=0x323339
        )
        
        description_lines = []
        for alias_name, command in aliases.items():
            description_lines.append(f"`{alias_name}` → `{command}`")
        
        embed.description = "\n".join(description_lines)
        
        if len(embed.description) > 2048:
            # If too long, use pagination
            pages = []
            current_page = []
            current_length = 0
            
            for line in description_lines:
                if current_length + len(line) + 1 > 2048:
                    pages.append("\n".join(current_page))
                    current_page = [line]
                    current_length = len(line)
                else:
                    current_page.append(line)
                    current_length += len(line) + 1
            
            if current_page:
                pages.append("\n".join(current_page))
            
            embeds = []
            for i, page in enumerate(pages):
                page_embed = discord.Embed(
                    title=f"Aliases for {ctx.guild.name} (Page {i+1}/{len(pages)})",
                    description=page,
                    color=0x323339
                )
                embeds.append(page_embed)
            
            await pagination.Paginator(embeds).start(ctx)
        else:
            await ctx.send_or_reply(embed=embed)

    async def process_alias(self, message):
        """Process potential alias commands"""
        if not message.guild or message.author.bot:
            return False
            
        # Get the command name from the message
        ctx = await self.bot.get_context(message)
        if ctx.command:  # Already a valid command
            return False
            
        # Check if it's a potential alias
        content = message.content
        for prefix in self.bot.get_guild_prefixes(message.guild):
            if content.startswith(prefix):
                command_part = content[len(prefix):].strip()
                if not command_part:
                    continue
                    
                args = command_part.split()
                alias_name = args[0]
                user_args = args[1:] if len(args) > 1 else []
                
                # Check if this is an alias
                alias_command = await self.get_alias(message.guild.id, alias_name)
                if alias_command:
                    try:
                        # Substitute arguments
                        final_command = self.substitute_arguments(alias_command, user_args)

                        # Create new message content with the substituted command
                        new_content = prefix + final_command

                        # Create a new message object with the substituted content
                        message.content = new_content

                        # Process the command
                        await self.bot.process_commands(message)
                        return True
                    except Exception as e:
                        # Log the error but don't crash
                        print(f"Error processing alias '{alias_name}': {e}")
                        return False
        
        return False

# Custom Commands (Aliases) Documentation

The Custom Commands feature allows server administrators to create shortcuts (aliases) that invoke other commands. This is useful for creating shorter or more memorable commands for your server.

## Commands

### `alias add <shortcut> <command>`
Creates a new command alias.

**Permission Required:** Manage Guild
**Usage:** `,alias add <shortcut> <command>`

**Examples:**
```
,alias add ban timeout {0} 10m
,alias add pic role {0} @image-role
,alias add shh timeout {0} 10m
```

### `alias remove <shortcut>`
Removes an existing alias.

**Permission Required:** Manage Guild
**Usage:** `,alias remove <shortcut>`
**Aliases:** `alias delete`, `alias rm`

**Example:**
```
,alias remove ban
```

### `alias view <shortcut>`
Shows what command an alias executes.

**Usage:** `,alias view <shortcut>`

**Example:**
```
,alias view ban
```

### `alias list`
Lists all aliases for the current server.

**Usage:** `,alias list`

## Argument Substitution

You can use predefined arguments in your aliases. To use the user's input as arguments, use `{0}`, `{1}`, `{2}`, etc.

- `{0}` = First argument from user input
- `{1}` = Second argument from user input
- `{2}` = Third argument from user input
- And so on...

### Examples

**Creating an alias with predefined arguments:**
```
,alias add shh timeout {0} 10m
```
When a user runs `,shh @jonathan`, it executes `,timeout @jonathan 10m`

**Creating an alias for role management:**
```
,alias add pic role {0} @image-role
```
When a user runs `,pic @jonathan`, it executes `,role @jonathan @image-role`

**Multiple arguments:**
```
,alias add customban ban {0} {1}
```
When a user runs `,customban @jonathan Harassing Members`, it executes `,ban @jonathan Harassing Members`

## Features

- **Server-specific:** Each server has its own set of aliases
- **Permission-based:** Only users with Manage Guild permission can create/remove aliases
- **Argument substitution:** Use `{0}`, `{1}`, etc. for dynamic arguments
- **Command validation:** The system checks that the target command exists
- **Conflict prevention:** Cannot create aliases with names that match existing commands
- **Caching:** Aliases are cached for fast execution

## Technical Details

- Aliases are stored in the database and persist across bot restarts
- The system processes aliases when no matching command is found
- Arguments are substituted before command execution
- Aliases support all the same features as regular commands (permissions, cooldowns, etc.)

## Limitations

- Cannot create aliases for aliases (no chaining)
- Alias names cannot conflict with existing command names
- Maximum alias name length follows Discord's message limits
- Arguments must be used in order ({0}, {1}, {2}, etc.)

## Database Schema

The aliases are stored in the `aliases` table with the following structure:
- `id`: Unique identifier
- `server_id`: Discord server ID
- `alias_name`: The shortcut name
- `command`: The command to execute
- `created_by`: User ID who created the alias
- `created_at`: Timestamp of creation
